[{"identifier": "DASH-1001", "title": "Fix login redirect loop in Safari", "labels": ["Refa<PERSON>"], "project": "Design System", "assignee": "<PERSON>", "dueDate": "2025-06-04", "status": "Done", "priority": "Low", "description": "Into including simple by forward their kind suddenly. When bill pull store cut media foreign benefit.", "createdAt": "2025-04-22T10:36:25", "updatedAt": "2025-05-15T14:55:37", "cycle": "Q2 Planning", "estimate": 8}, {"identifier": "DASH-1002", "title": "Add tooltips to dashboard icons", "labels": ["Enhancement"], "project": "Dashboard", "assignee": "<PERSON>", "dueDate": "2025-05-28", "status": "In Progress", "priority": "<PERSON><PERSON>", "description": "Style fish argue discuss will plant. Seat pick must so.", "createdAt": "2025-05-01T18:03:14", "updatedAt": "2025-05-02T23:21:08", "cycle": "UI Cleanup", "estimate": 2}, {"identifier": "DASH-1003", "title": "Sync billing history from Stripe", "labels": ["Performance"], "project": "Dashboard", "assignee": "<PERSON>", "dueDate": "2025-06-02", "status": "Backlog", "priority": "Low", "description": "Fine someone write purpose situation. Sing stuff data summer thank material respond while. Enjoy interest special risk.", "createdAt": "2025-04-26T21:03:29", "updatedAt": "2025-05-08T00:36:29", "cycle": "UI Cleanup", "estimate": 13}, {"identifier": "DASH-1004", "title": "Refactor legacy table components", "labels": ["Refa<PERSON>"], "project": "<PERSON><PERSON>amp", "assignee": "<PERSON>", "dueDate": "2025-06-01", "status": "In Review", "priority": "Low", "description": "Television effect of toward ball. Animal artist leader run run home activity government. Radio everything speak article amount ball light. Baby benefit yard five although.", "createdAt": "2025-04-22T17:12:40", "updatedAt": "2025-05-09T20:29:50", "cycle": "UI Cleanup", "estimate": 2}, {"identifier": "DASH-1005", "title": "Fix bug in rescan wizard showing incorrect error", "labels": ["Performance"], "project": "<PERSON><PERSON>", "assignee": "<PERSON>", "dueDate": "2025-05-22", "status": "In Review", "priority": "Medium", "description": "Woman radio hundred light expect deal. Same minute forget pressure reflect turn score. Yet take sense several.", "createdAt": "2025-04-21T00:26:40", "updatedAt": "2025-05-14T00:33:53", "cycle": "Sprint 2", "estimate": 3}, {"identifier": "DASH-1006", "title": "Add keyboard navigation to modal dialogs", "labels": ["Refa<PERSON>"], "project": "Billing System", "assignee": "<PERSON>", "dueDate": "2025-06-02", "status": "Todo", "priority": "High", "description": "Word vote Democrat others magazine. Current for discuss level stage daughter. Off draw open fact line sister for.", "createdAt": "2025-05-02T13:48:05", "updatedAt": "2025-05-11T13:45:32", "cycle": "Sprint 2", "estimate": 1}, {"identifier": "DASH-1007", "title": "Optimize table rendering performance", "labels": ["Bug"], "project": "<PERSON>an <PERSON>", "assignee": "<PERSON>", "dueDate": "2025-05-21", "status": "In Progress", "priority": "<PERSON><PERSON>", "description": "Get special economic member surface. Consider scientist blue lawyer smile meet various.", "createdAt": "2025-05-03T22:09:38", "updatedAt": "2025-05-13T19:03:13", "cycle": "UI Cleanup", "estimate": 1}, {"identifier": "DASH-1008", "title": "Improve accessibility in dark mode", "labels": ["Refa<PERSON>"], "project": "UX Polish", "assignee": "<PERSON>", "dueDate": "2025-05-25", "status": "Todo", "priority": "Low", "description": "Stand nation close suddenly letter. Training day media me able machine.", "createdAt": "2025-04-30T09:51:05", "updatedAt": "2025-05-08T14:25:35", "cycle": "Security Audit", "estimate": 8}, {"identifier": "DASH-1009", "title": "Handle expired JWT tokens gracefully", "labels": ["Bug"], "project": "Trust Center", "assignee": "<PERSON>", "dueDate": "2025-05-27", "status": "Todo", "priority": "Low", "description": "List player Mrs southern weight type reveal line. Deal total security near. Father wall civil election.", "createdAt": "2025-04-30T07:03:25", "updatedAt": "2025-05-03T10:57:52", "cycle": "Sprint 2", "estimate": 13}, {"identifier": "DASH-1010", "title": "Implement optimistic UI updates for issue status", "labels": ["Triage"], "project": "UX Polish", "assignee": "<PERSON>", "dueDate": "2025-05-30", "status": "Done", "priority": "High", "description": "Rock role year open that partner. Choose laugh owner look.", "createdAt": "2025-04-18T15:21:41", "updatedAt": "2025-04-27T18:37:22", "cycle": "UI Cleanup", "estimate": 13}, {"identifier": "DASH-1011", "title": "Fix race condition in scan scheduler", "labels": ["Bug"], "project": "Billing System", "assignee": "<PERSON>", "dueDate": "2025-05-21", "status": "Done", "priority": "High", "description": "Treatment social value back better. Kitchen term garden various listen one.", "createdAt": "2025-04-19T08:33:10", "updatedAt": "2025-05-02T13:16:13", "cycle": "Security Audit", "estimate": 13}, {"identifier": "DASH-1012", "title": "Rework permission roles on project settings", "labels": ["Enhancement"], "project": "<PERSON><PERSON>", "assignee": "<PERSON>", "dueDate": "2025-05-25", "status": "In Progress", "priority": "Medium", "description": "Drive very surface term near. Economic agree practice also society successful on. Nothing again in add line fear conference believe.", "createdAt": "2025-04-24T17:50:53", "updatedAt": "2025-05-10T16:54:55", "cycle": "Sprint 1", "estimate": 5}, {"identifier": "DASH-1013", "title": "Migrate user data to new schema", "labels": ["Bug"], "project": "<PERSON>an <PERSON>", "assignee": "<PERSON>", "dueDate": "2025-05-26", "status": "Backlog", "priority": "<PERSON><PERSON>", "description": "Garden ever sport receive seek church.", "createdAt": "2025-04-30T12:25:47", "updatedAt": "2025-05-01T08:36:38", "cycle": "Security Audit", "estimate": 13}, {"identifier": "DASH-1014", "title": "Reduce API response time on dashboard load", "labels": ["Integration"], "project": "<PERSON><PERSON>amp", "assignee": "<PERSON>", "dueDate": "2025-05-22", "status": "In Review", "priority": "Medium", "description": "Sound store west. Still across detail middle sometimes right case mention. Develop personal tax agent true cost.", "createdAt": "2025-04-21T14:19:37", "updatedAt": "2025-05-02T15:28:19", "cycle": "Sprint 1", "estimate": 2}, {"identifier": "DASH-1015", "title": "Fix typo in error messages", "labels": ["Bug"], "project": "<PERSON><PERSON>amp", "assignee": "<PERSON>", "dueDate": "2025-05-27", "status": "In Progress", "priority": "Medium", "description": "Contain provide nation east Republican. Even boy nice many. Property statement understand perhaps drug.", "createdAt": "2025-05-03T21:36:36", "updatedAt": "2025-05-08T02:43:50", "cycle": "Sprint 1", "estimate": 13}, {"identifier": "DASH-1016", "title": "Improve sorting on issues table", "labels": ["Integration"], "project": "<PERSON><PERSON>amp", "assignee": "<PERSON>", "dueDate": "2025-06-03", "status": "Backlog", "priority": "Medium", "description": "Traditional physical who really. Wrong cultural policy hundred shake indicate anyone stop.", "createdAt": "2025-05-05T09:44:05", "updatedAt": "2025-05-07T10:06:34", "cycle": "Security Audit", "estimate": 5}, {"identifier": "DASH-1017", "title": "Add toggle for beta features", "labels": ["Bug", "Front-end"], "project": "Design System", "assignee": "<PERSON>", "dueDate": "2025-05-20", "status": "In Progress", "priority": "Medium", "description": "Computer foot rule story. Continue news agreement central. Part us worker environmental scientist million.", "createdAt": "2025-04-27T23:22:30", "updatedAt": "2025-05-13T19:44:39", "cycle": "UI Cleanup", "estimate": 1}, {"identifier": "DASH-1018", "title": "Log out user after password change", "labels": ["Performance"], "project": "<PERSON>an <PERSON>", "assignee": "<PERSON>", "dueDate": "2025-05-25", "status": "In Progress", "priority": "Low", "description": "Other participant next few relationship head word.", "createdAt": "2025-05-10T13:49:47", "updatedAt": "2025-05-16T03:42:42", "cycle": "UI Cleanup", "estimate": 5}, {"identifier": "DASH-1019", "title": "Add deep link support to notifications", "labels": ["Enhancement"], "project": "<PERSON>an <PERSON>", "assignee": "<PERSON>", "dueDate": "2025-06-03", "status": "Backlog", "priority": "Low", "description": "Indicate significant what full station time condition. Development identify recently class they though teach reason.", "createdAt": "2025-04-28T18:34:48", "updatedAt": "2025-05-07T16:39:43", "cycle": "Q2 Planning", "estimate": 5}, {"identifier": "DASH-1020", "title": "Update icons to new design system", "labels": ["Security"], "project": "<PERSON><PERSON>amp", "assignee": "<PERSON>", "dueDate": "2025-05-20", "status": "In Review", "priority": "High", "description": "Side represent business effect who. Begin about business way major.", "createdAt": "2025-05-04T14:10:03", "updatedAt": "2025-05-13T19:18:34", "cycle": "Sprint 1", "estimate": 5}, {"identifier": "DASH-1021", "title": "Add audit log export as CSV", "labels": ["Integration"], "project": "Dashboard", "assignee": "<PERSON>", "dueDate": "2025-06-05", "status": "Backlog", "priority": "Medium", "description": "Scene quite evidence its. Arm week better chance product time catch sea. Child all there option spend range human. At pass officer.", "createdAt": "2025-05-09T20:53:16", "updatedAt": "2025-05-10T15:10:49", "cycle": "Security Audit", "estimate": 5}, {"identifier": "DASH-1022", "title": "Fix tooltip position on hover", "labels": ["Performance"], "project": "Billing System", "assignee": "<PERSON>", "dueDate": "2025-06-02", "status": "In Review", "priority": "<PERSON><PERSON>", "description": "Test country guy field drop level. Majority send because in race. Tree customer reality always listen.", "createdAt": "2025-04-28T15:16:10", "updatedAt": "2025-05-11T20:37:53", "cycle": "Sprint 1", "estimate": 13}, {"identifier": "DASH-1023", "title": "Enable push notifications on mobile", "labels": ["Refa<PERSON>"], "project": "<PERSON><PERSON>amp", "assignee": "<PERSON>", "dueDate": "2025-05-24", "status": "Backlog", "priority": "<PERSON><PERSON>", "description": "Career trip training everybody. Great usually left push language.", "createdAt": "2025-04-21T10:06:58", "updatedAt": "2025-05-08T06:19:51", "cycle": "Sprint 2", "estimate": 8}, {"identifier": "DASH-1024", "title": "Add context-aware help tooltips", "labels": ["Integration"], "project": "Trust Center", "assignee": "<PERSON>", "dueDate": "2025-05-24", "status": "In Review", "priority": "High", "description": "Speak job news artist food picture you. Wrong paper professional about. Reveal tonight thousand national training son stuff.", "createdAt": "2025-05-01T01:06:37", "updatedAt": "2025-05-03T22:56:56", "cycle": "Q2 Planning", "estimate": 3}, {"identifier": "DASH-1025", "title": "Fix animation glitch in sidebar collapse", "labels": ["Refa<PERSON>"], "project": "<PERSON><PERSON>", "assignee": "<PERSON>", "dueDate": "2025-05-18", "status": "Backlog", "priority": "Low", "description": "Give serve beyond account far place painting financial.", "createdAt": "2025-05-02T00:46:54", "updatedAt": "2025-05-10T13:00:08", "cycle": "UI Cleanup", "estimate": 3}, {"identifier": "DASH-1026", "title": "Support grouped filters for issue view", "labels": ["Bug", "Front-end"], "project": "UX Polish", "assignee": "<PERSON>", "dueDate": "2025-05-31", "status": "Todo", "priority": "<PERSON><PERSON>", "description": "International since open show thank top. Benefit mother notice protect through girl figure. Lose star boy training for travel politics.", "createdAt": "2025-04-30T17:57:51", "updatedAt": "2025-05-04T09:57:57", "cycle": "UI Cleanup", "estimate": 2}, {"identifier": "DASH-1027", "title": "Enable per-user theme settings", "labels": ["Refa<PERSON>"], "project": "<PERSON>an <PERSON>", "assignee": "<PERSON>", "dueDate": "2025-05-21", "status": "Backlog", "priority": "<PERSON><PERSON>", "description": "Yard growth management method establish follow group seat. Because left this officer sure Mr pass. Century small high security.", "createdAt": "2025-05-10T01:21:13", "updatedAt": "2025-05-15T12:48:05", "cycle": "Sprint 1", "estimate": 5}, {"identifier": "DASH-1028", "title": "Migrate CI pipeline to GitHub Actions", "labels": ["Bug", "Front-end"], "project": "<PERSON><PERSON>amp", "assignee": "<PERSON>", "dueDate": "2025-05-19", "status": "Done", "priority": "<PERSON><PERSON>", "description": "Above husband all ago rich protect least. New store partner head financial once time. His full think rule including site.", "createdAt": "2025-04-24T06:18:11", "updatedAt": "2025-05-09T04:08:42", "cycle": "Q2 Planning", "estimate": 1}, {"identifier": "DASH-1029", "title": "Implement session keep-alive", "labels": ["Performance"], "project": "Dashboard", "assignee": "<PERSON>", "dueDate": "2025-05-18", "status": "Todo", "priority": "Medium", "description": "Maybe result responsibility company. Sure discover chair. But seek never consumer happy.", "createdAt": "2025-05-08T00:56:36", "updatedAt": "2025-05-12T18:35:22", "cycle": "Q2 Planning", "estimate": 5}, {"identifier": "DASH-1030", "title": "Update all dependencies", "labels": ["Refa<PERSON>"], "project": "<PERSON><PERSON>", "assignee": "<PERSON>", "dueDate": "2025-05-19", "status": "In Progress", "priority": "Medium", "description": "Public scientist actually. List star adult require professional yourself. Have station trouble would authority real. Mind business run bring coach although someone cold.", "createdAt": "2025-05-07T20:33:28", "updatedAt": "2025-05-13T17:23:27", "cycle": "Sprint 2", "estimate": 1}, {"identifier": "DASH-1031", "title": "Add avatar upload to profile settings", "labels": ["Bug", "Front-end"], "project": "Dashboard", "assignee": "<PERSON>", "dueDate": "2025-06-01", "status": "Done", "priority": "Medium", "description": "A that thought sound window. Could property seat term sense. Develop dog operation sport.", "createdAt": "2025-04-20T03:52:41", "updatedAt": "2025-04-27T06:08:14", "cycle": "Sprint 2", "estimate": 2}, {"identifier": "DASH-1032", "title": "Audit password reset rate limits", "labels": ["Refa<PERSON>"], "project": "Dashboard", "assignee": "<PERSON>", "dueDate": "2025-05-25", "status": "Todo", "priority": "<PERSON><PERSON>", "description": "Father design especially safe suggest strong response. Public along this do rich life.", "createdAt": "2025-04-29T21:51:43", "updatedAt": "2025-05-11T05:49:54", "cycle": "Sprint 2", "estimate": 3}, {"identifier": "DASH-1033", "title": "Fix mobile layout for notifications page", "labels": ["Bug"], "project": "Design System", "assignee": "<PERSON>", "dueDate": "2025-05-19", "status": "In Progress", "priority": "Medium", "description": "Level can chair American term hard. Itself probably believe either see condition.", "createdAt": "2025-04-24T20:32:34", "updatedAt": "2025-05-11T11:49:30", "cycle": "UI Cleanup", "estimate": 5}, {"identifier": "DASH-1034", "title": "Update changelog formatting", "labels": ["Security"], "project": "<PERSON>an <PERSON>", "assignee": "<PERSON>", "dueDate": "2025-05-29", "status": "Done", "priority": "Low", "description": "Reason miss shoulder project certain. Local local little increase story produce find. Officer trade tough through.", "createdAt": "2025-04-16T10:32:05", "updatedAt": "2025-05-15T11:20:39", "cycle": "UI Cleanup", "estimate": 1}, {"identifier": "DASH-1035", "title": "Add user status indicator", "labels": ["Performance"], "project": "<PERSON>an <PERSON>", "assignee": "<PERSON>", "dueDate": "2025-06-04", "status": "Backlog", "priority": "High", "description": "Ahead budget learn similar hold. Policy what full deep value down. Seem current land similar beautiful form.", "createdAt": "2025-04-16T22:03:50", "updatedAt": "2025-05-13T02:18:46", "cycle": "Sprint 2", "estimate": 5}, {"identifier": "DASH-1036", "title": "Add skeleton loaders for list view", "labels": ["UI"], "project": "<PERSON><PERSON>amp", "assignee": "<PERSON>", "dueDate": "2025-05-25", "status": "In Review", "priority": "Low", "description": "Anyone training work turn fight song data cold. Worry ability happen add.", "createdAt": "2025-05-01T01:20:10", "updatedAt": "2025-05-02T10:29:55", "cycle": "UI Cleanup", "estimate": 2}, {"identifier": "DASH-1037", "title": "Enable advanced filtering on team view", "labels": ["Security"], "project": "Billing System", "assignee": "<PERSON>", "dueDate": "2025-05-19", "status": "In Progress", "priority": "<PERSON><PERSON>", "description": "Adult relationship war program recognize position professor. Mother conference evidence. Every effect daughter college focus we.", "createdAt": "2025-05-07T09:30:32", "updatedAt": "2025-05-13T17:21:02", "cycle": "Q2 Planning", "estimate": 8}, {"identifier": "DASH-1038", "title": "Fix duplicate events in timeline", "labels": ["UI"], "project": "UX Polish", "assignee": "<PERSON>", "dueDate": "2025-06-04", "status": "Todo", "priority": "Low", "description": "Data manager reflect born.", "createdAt": "2025-04-29T12:50:54", "updatedAt": "2025-05-02T09:03:19", "cycle": "Q2 Planning", "estimate": 5}, {"identifier": "DASH-1039", "title": "Cleanup unused GraphQL fields", "labels": ["Bug"], "project": "Billing System", "assignee": "<PERSON>", "dueDate": "2025-05-20", "status": "In Progress", "priority": "<PERSON><PERSON>", "description": "Old enter term national. Store know right prove respond.", "createdAt": "2025-04-23T06:08:20", "updatedAt": "2025-05-02T10:54:13", "cycle": "Sprint 1", "estimate": 8}, {"identifier": "DASH-1040", "title": "Normalize timezone handling in backend", "labels": ["UI"], "project": "<PERSON><PERSON>amp", "assignee": "<PERSON>", "dueDate": "2025-05-21", "status": "Backlog", "priority": "Low", "description": "Behavior full space move. Produce show hour research environment center. Type difference cover candidate some few war.", "createdAt": "2025-05-10T16:03:37", "updatedAt": "2025-05-12T17:58:22", "cycle": "Security Audit", "estimate": 8}, {"identifier": "DASH-1041", "title": "Fix overflow in mobile filter drawer", "labels": ["Refa<PERSON>"], "project": "<PERSON><PERSON>amp", "assignee": "<PERSON>", "dueDate": "2025-05-29", "status": "In Progress", "priority": "Medium", "description": "Action cell wall rich professional some unit democratic. Bed individual center environment speak nor.", "createdAt": "2025-05-04T02:26:43", "updatedAt": "2025-05-15T10:26:20", "cycle": "Sprint 1", "estimate": 2}, {"identifier": "DASH-1042", "title": "Improve copy on onboarding page", "labels": ["Triage"], "project": "<PERSON><PERSON>", "assignee": "<PERSON>", "dueDate": "2025-05-17", "status": "Backlog", "priority": "Low", "description": "Meeting name cup management nice teach. Interest amount accept free treatment point campaign. Best onto sport always under.", "createdAt": "2025-04-23T15:45:41", "updatedAt": "2025-05-14T23:37:31", "cycle": "Q2 Planning", "estimate": 1}, {"identifier": "DASH-1043", "title": "Fix layout shift in modals", "labels": ["Refa<PERSON>"], "project": "Trust Center", "assignee": "<PERSON>", "dueDate": "2025-06-05", "status": "Todo", "priority": "Medium", "description": "Glass fear pretty positive. Billion boy piece simply.", "createdAt": "2025-04-20T13:11:04", "updatedAt": "2025-05-15T01:56:44", "cycle": "Q2 Planning", "estimate": 2}, {"identifier": "DASH-1044", "title": "Allow bulk delete of test data", "labels": ["Triage"], "project": "<PERSON>an <PERSON>", "assignee": "<PERSON>", "dueDate": "2025-05-25", "status": "Done", "priority": "Low", "description": "Prove impact brother practice particularly order that clearly. Fact spend child you.", "createdAt": "2025-05-04T22:01:29", "updatedAt": "2025-05-06T12:05:16", "cycle": "UI Cleanup", "estimate": 8}, {"identifier": "DASH-1045", "title": "Add feature flag support", "labels": ["Refa<PERSON>"], "project": "<PERSON><PERSON>amp", "assignee": "<PERSON>", "dueDate": "2025-06-05", "status": "Backlog", "priority": "Medium", "description": "Want top tonight style. Never director too best. Skin follow so little parent impact raise.", "createdAt": "2025-04-26T14:37:22", "updatedAt": "2025-04-26T15:54:19", "cycle": "UI Cleanup", "estimate": 13}, {"identifier": "DASH-1046", "title": "Improve error logging structure", "labels": ["Bug", "Front-end"], "project": "<PERSON>an <PERSON>", "assignee": "<PERSON>", "dueDate": "2025-05-19", "status": "Todo", "priority": "Medium", "description": "Health tell little form energy full owner. Local bad threat community can visit. Plant you series physical mind name.", "createdAt": "2025-04-24T22:12:34", "updatedAt": "2025-05-07T01:56:43", "cycle": "Sprint 1", "estimate": 13}, {"identifier": "DASH-1047", "title": "Add integration with Slack for alerts", "labels": ["Feature", "Back-end"], "project": "<PERSON><PERSON>amp", "assignee": "<PERSON>", "dueDate": "2025-05-29", "status": "Todo", "priority": "High", "description": "Market season case vote wish huge. Wind environmental together reduce again trouble five.", "createdAt": "2025-04-27T07:13:29", "updatedAt": "2025-05-01T02:31:20", "cycle": "Sprint 2", "estimate": 13}, {"identifier": "DASH-1048", "title": "Fix crash on empty search query", "labels": ["Enhancement"], "project": "<PERSON><PERSON>amp", "assignee": "<PERSON>", "dueDate": "2025-05-26", "status": "Backlog", "priority": "<PERSON><PERSON>", "description": "Against morning expert represent own again argue site. My music oil fast per weight. Quite top girl control mother. Well choice wide.", "createdAt": "2025-04-20T04:48:36", "updatedAt": "2025-04-20T17:00:32", "cycle": "Security Audit", "estimate": 1}, {"identifier": "DASH-1049", "title": "Validate email input in invitation form", "labels": ["UI"], "project": "<PERSON>an <PERSON>", "assignee": "<PERSON>", "dueDate": "2025-05-27", "status": "In Review", "priority": "Medium", "description": "Any company market time go our decide. Accept local before true exactly.", "createdAt": "2025-04-16T12:56:51", "updatedAt": "2025-05-03T03:15:00", "cycle": "Sprint 2", "estimate": 1}, {"identifier": "DASH-1050", "title": "Fix tooltip text overflow", "labels": ["Security"], "project": "Billing System", "assignee": "<PERSON>", "dueDate": "2025-05-29", "status": "Backlog", "priority": "Medium", "description": "Service form sport material know hospital. Investment upon red board involve less technology.", "createdAt": "2025-05-07T09:25:08", "updatedAt": "2025-05-11T21:17:20", "cycle": "UI Cleanup", "estimate": 3}]