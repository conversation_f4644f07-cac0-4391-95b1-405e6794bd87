"use client";

import { use<PERSON><PERSON> } from 'jotai';
import { paginationAtom, PaginationState } from '@/store/tableStore';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ChevronLeft, ChevronRight, ChevronsLeft, ChevronsRight } from 'lucide-react';

interface PaginationControlsProps {
  totalCount: number;
  pageCount: number;
  currentPage: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}

const pageSizeOptions = [5, 10, 20, 50, 100];

export default function PaginationControls({
  totalCount,
  pageCount,
  currentPage,
  hasNextPage,
  hasPreviousPage,
}: PaginationControlsProps) {
  const [pagination, setPagination] = useAtom(paginationAtom);

  const updatePagination = (updates: Partial<PaginationState>) => {
    setPagination({ ...pagination, ...updates });
  };

  const goToFirstPage = () => updatePagination({ pageIndex: 0 });
  const goToPreviousPage = () => updatePagination({ pageIndex: Math.max(0, currentPage - 1) });
  const goToNextPage = () => updatePagination({ pageIndex: Math.min(pageCount - 1, currentPage + 1) });
  const goToLastPage = () => updatePagination({ pageIndex: Math.max(0, pageCount - 1) });

  const changePageSize = (newPageSize: string) => {
    const pageSize = parseInt(newPageSize);
    // Reset to first page when changing page size
    updatePagination({ pageSize, pageIndex: 0 });
  };

  if (totalCount === 0) {
    return (
      <div className="flex items-center justify-between px-2 py-4">
        <div className="text-sm text-muted-foreground">
          No results found
        </div>
      </div>
    );
  }

  const startItem = currentPage * pagination.pageSize + 1;
  const endItem = Math.min((currentPage + 1) * pagination.pageSize, totalCount);

  return (
    <div className="flex items-center justify-between px-2 py-4">
      <div className="flex items-center space-x-6 lg:space-x-8">
        <div className="flex items-center space-x-2">
          <p className="text-sm font-medium">Rows per page</p>
          <Select value={pagination.pageSize.toString()} onValueChange={changePageSize}>
            <SelectTrigger className="h-8 w-[70px]">
              <SelectValue />
            </SelectTrigger>
            <SelectContent side="top">
              {pageSizeOptions.map((size) => (
                <SelectItem key={size} value={size.toString()}>
                  {size}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        
        <div className="flex w-[100px] items-center justify-center text-sm font-medium">
          Page {currentPage + 1} of {Math.max(1, pageCount)}
        </div>
        
        <div className="text-sm text-muted-foreground">
          {startItem}-{endItem} of {totalCount} results
        </div>
      </div>
      
      <div className="flex items-center space-x-2">
        <Button
          variant="outline"
          className="hidden h-8 w-8 p-0 lg:flex"
          onClick={goToFirstPage}
          disabled={!hasPreviousPage}
        >
          <span className="sr-only">Go to first page</span>
          <ChevronsLeft className="h-4 w-4" />
        </Button>
        
        <Button
          variant="outline"
          className="h-8 w-8 p-0"
          onClick={goToPreviousPage}
          disabled={!hasPreviousPage}
        >
          <span className="sr-only">Go to previous page</span>
          <ChevronLeft className="h-4 w-4" />
        </Button>
        
        <Button
          variant="outline"
          className="h-8 w-8 p-0"
          onClick={goToNextPage}
          disabled={!hasNextPage}
        >
          <span className="sr-only">Go to next page</span>
          <ChevronRight className="h-4 w-4" />
        </Button>
        
        <Button
          variant="outline"
          className="hidden h-8 w-8 p-0 lg:flex"
          onClick={goToLastPage}
          disabled={!hasNextPage}
        >
          <span className="sr-only">Go to last page</span>
          <ChevronsRight className="h-4 w-4" />
        </Button>
      </div>
    </div>
  );
}
