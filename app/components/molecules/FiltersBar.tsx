"use client";

import { use<PERSON><PERSON> } from 'jotai';
import { filtersAtom, FilterState } from '@/store/tableStore';
import FilterDropdown from './FilterDropdown';
import { Button } from '@/components/ui/button';
import { X } from 'lucide-react';

interface FiltersBarProps {
  data: any[];
}

export default function FiltersBar({ data }: FiltersBarProps) {
  const [filters, setFilters] = useAtom(filtersAtom);

  // Extract unique values for each filter
  const uniqueStatuses = [...new Set(data.map(item => item.status).filter(Boolean))];
  const uniqueProjects = [...new Set(data.map(item => item.project).filter(Boolean))];
  const uniquePriorities = [...new Set(data.map(item => item.priority).filter(Boolean))];
  const uniqueAssignees = [...new Set(data.map(item => item.assignee).filter(Boolean))];
  const uniqueCycles = [...new Set(data.map(item => item.cycle).filter(<PERSON>olean))];

  const updateFilter = (key: keyof FilterState, value: string) => {
    setFilters({ ...filters, [key]: value });
  };

  const clearAllFilters = () => {
    setFilters({
      status: "",
      project: "",
      priority: "",
      assignee: "",
      cycle: "",
    });
  };

  const hasActiveFilters = Object.values(filters).some(value => value !== "");

  return (
    <div className="flex flex-wrap gap-3 items-center">
      <FilterDropdown
        value={filters.status}
        onChange={(value) => updateFilter('status', value)}
        options={uniqueStatuses}
        placeholder="Status"
      />
      
      <FilterDropdown
        value={filters.project}
        onChange={(value) => updateFilter('project', value)}
        options={uniqueProjects}
        placeholder="Project"
      />
      
      <FilterDropdown
        value={filters.priority}
        onChange={(value) => updateFilter('priority', value)}
        options={uniquePriorities}
        placeholder="Priority"
      />
      
      <FilterDropdown
        value={filters.assignee}
        onChange={(value) => updateFilter('assignee', value)}
        options={uniqueAssignees}
        placeholder="Assignee"
      />
      
      <FilterDropdown
        value={filters.cycle}
        onChange={(value) => updateFilter('cycle', value)}
        options={uniqueCycles}
        placeholder="Cycle"
      />

      {hasActiveFilters && (
        <Button
          variant="outline"
          size="sm"
          onClick={clearAllFilters}
          className="h-10"
        >
          <X className="h-4 w-4 mr-1" />
          Clear Filters
        </Button>
      )}
    </div>
  );
}
