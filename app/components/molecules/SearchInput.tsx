"use client";

import { useAtom } from 'jotai';
import { useState, useEffect } from 'react';
import { searchAtom } from '@/store/tableStore';
import { useDebounce } from '@/hooks/useDebounce';
import { Input } from "@/components/ui/input";

export default function SearchInput() {
  const [search, setSearch] = useAtom(searchAtom);
  const [localSearch, setLocalSearch] = useState(search);
  const debouncedSearch = useDebounce(localSearch, 300);

  // Update Jotai atom when debounced value changes
  useEffect(() => {
    setSearch(debouncedSearch);
  }, [debouncedSearch, setSearch]);

  // Sync local state when atom changes externally (e.g., reset)
  useEffect(() => {
    setLocalSearch(search);
  }, [search]);

  return (
    <Input
      placeholder="Search by title or identifier..."
      value={localSearch}
      onChange={(e) => setLocalSearch(e.target.value)}
      className="w-full sm:w-64"
    />
  );
}
