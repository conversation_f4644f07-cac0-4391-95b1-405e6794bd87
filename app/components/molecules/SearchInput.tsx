"use client";

import { useAtom } from 'jotai';
import { searchAtom } from '@/store/tableStore';
import { Input } from "@/components/ui/input";

export default function SearchInput() {
  const [search, setSearch] = useAtom(searchAtom);

  return (
    <Input
      placeholder="Search by title or identifier..."
      value={search}
      onChange={(e) => setSearch(e.target.value)}
      className="w-full sm:w-64"
    />
  );
}
