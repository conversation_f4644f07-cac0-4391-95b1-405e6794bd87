"use client";

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

export type GroupByOption = "none" | "status" | "assignee" | "cycle" | "estimate";

type GroupByDropdownProps = {
  value: GroupByOption;
  onChange: (value: GroupByOption) => void;
};

const groupByOptions: { value: GroupByOption; label: string }[] = [
  { value: "none", label: "None" },
  { value: "status", label: "Status" },
  { value: "assignee", label: "Assignee" },
  { value: "cycle", label: "Cycle" },
  { value: "estimate", label: "Estimate" },
];

export default function GroupByDropdown({
  value,
  onChange,
}: GroupByDropdownProps) {
  return (
    <div className="flex items-center gap-2">
      <span className="text-sm font-medium">Group by:</span>
      <Select value={value} onValueChange={onChange}>
        <SelectTrigger className="w-32">
          <SelectValue placeholder="Group by" />
        </SelectTrigger>
        <SelectContent>
          {groupByOptions.map((option) => (
            <SelectItem key={option.value} value={option.value}>
              {option.label}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
}
