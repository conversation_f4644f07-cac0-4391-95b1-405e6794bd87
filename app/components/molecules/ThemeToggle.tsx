"use client";

import { use<PERSON>tom, useSet<PERSON>tom } from 'jotai';
import { themeAtom, toggleThemeAtom } from '@/store/themeStore';
import { Button } from '@/components/ui/button';
import { Moon, Sun } from 'lucide-react';

export default function ThemeToggle() {
  const [theme] = useAtom(themeAtom);
  const toggleTheme = useSetAtom(toggleThemeAtom);

  return (
    <Button
      variant="outline"
      size="icon"
      onClick={() => toggleTheme()}
      className="h-9 w-9"
    >
      {theme === 'light' ? (
        <Moon className="h-4 w-4" />
      ) : (
        <Sun className="h-4 w-4" />
      )}
      <span className="sr-only">Toggle theme</span>
    </Button>
  );
}
