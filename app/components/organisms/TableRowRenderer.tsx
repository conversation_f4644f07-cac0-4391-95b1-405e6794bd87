import { TableCell, TableRow } from "@/components/ui/table";
import { flexRender, Row } from "@tanstack/react-table";
import { Minus, Plus } from "lucide-react";
import React from "react";

interface TableRowRendererProps {
  row: Row<any>;
  groupBy: "status" | "project";
  tableInstance: any;
}

export default function TableRowRenderer({
  row,
  groupBy,
  tableInstance,
}: TableRowRendererProps) {
  if (row.getIsGrouped()) {
    const isExpanded = row.getIsExpanded();

    return (
      <TableRow
        key={row.id}
        className="bg-muted font-semibold cursor-pointer"
        onClick={() => row.toggleExpanded()}>
        <TableCell colSpan={tableInstance.getAllLeafColumns().length}>
          <div className="flex items-center gap-2">
            <span className="text-muted-foreground">
              {isExpanded ? <Minus size={16} /> : <Plus size={16} />}{" "}
            </span>
            <span className="capitalize">{row.getValue(groupBy)}</span>
            <span className="text-xs text-muted-foreground ml-1">
              ({row.subRows.length} items)
            </span>
          </div>
        </TableCell>
      </TableRow>
    );
  }
  return (
    <TableRow key={row.id}>
      {row.getVisibleCells().map((cell: any) => (
        <TableCell key={cell.id}>
          {flexRender(cell.column.columnDef.cell, cell.getContext())}
        </TableCell>
      ))}
    </TableRow>
  );
}
