import { use<PERSON>tom } from 'jotai';
import { selectedIssueAtom } from '@/store/tableStore';
import { TableCell, TableRow } from "@/components/ui/table";
import { flexRender, Row } from "@tanstack/react-table";
import { Minus, Plus } from "lucide-react";
import React from "react";

interface TableRowRendererProps {
  row: Row<any>;
  groupBy: "none" | "status" | "assignee" | "cycle" | "estimate";
  tableInstance: any;
}

export default function TableRowRenderer({
  row,
  groupBy,
  tableInstance,
}: TableRowRendererProps) {
  const [, setSelectedIssueId] = useAtom(selectedIssueAtom);

  const handleRowClick = () => {
    if (!row.getIsGrouped()) {
      setSelectedIssueId(row.original.identifier);
    }
  };
  if (row.getIsGrouped() && groupBy !== "none") {
    // const isExpanded = row.getIsExpanded();
    const groupValue = row.getValue(groupBy);

    // Format group value based on the groupBy type
    const formatGroupValue = (value: any, type: string) => {
      if (value === null || value === undefined) {
        return "Unassigned";
      }

      switch (type) {
        case "estimate":
          return `${value} points`;
        case "assignee":
          return value || "Unassigned";
        default:
          return String(value);
      }
    };

    return (
      <TableRow
        key={row.id}
        className="bg-muted font-semibold"
        // onClick={() => row.toggleExpanded()} // Commented out expand/collapse functionality
        >
        <TableCell colSpan={tableInstance.getAllLeafColumns().length}>
          <div className="flex items-center gap-2">
            {/* Commented out expand/collapse icons */}
            {/* <span className="text-muted-foreground">
              {isExpanded ? <Minus size={16} /> : <Plus size={16} />}{" "}
            </span> */}
            <span className="capitalize">{formatGroupValue(groupValue, groupBy)}</span>
            <span className="text-xs text-muted-foreground ml-1">
              ({row.subRows.length} items)
            </span>
          </div>
        </TableCell>
      </TableRow>
    );
  }
  return (
    <TableRow
      key={row.id}
      className="cursor-pointer hover:bg-muted/50"
      onClick={handleRowClick}
    >
      {row.getVisibleCells().map((cell: any) => (
        <TableCell key={cell.id}>
          {flexRender(cell.column.columnDef.cell, cell.getContext())}
        </TableCell>
      ))}
    </TableRow>
  );
}
