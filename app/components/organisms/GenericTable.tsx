"use client";
import {ScrollArea} from "@/components/ui/scroll-area";
import {
    Table,
    TableBody,
    TableHead,
    TableHeader,
    TableRow,
} from "@/components/ui/table";
import {flexRender, Table as ReactTableInstance} from "@tanstack/react-table";
import React from "react";

interface GenericTableProps<TData> {
    table: ReactTableInstance<TData>;
    rowRenderer: (row: any, tableInstance: ReactTableInstance<TData>) => React.ReactNode;

}

export default function GenericTable<TData>({table, rowRenderer}: GenericTableProps<TData>) {
    return (
        <ScrollArea className="rounded border p-4 h-[32rem]"> {/* Consider making height configurable */}
            <Table className="relative">
                <TableHeader className="sticky top-0 bg-background z-10">
                    {table.getHeaderGroups().map((headerGroup) => (
                        <TableRow key={headerGroup.id}>
                            {headerGroup.headers.map((header) => (
                                <TableHead key={header.id}>
                                    {header.isPlaceholder
                                        ? null
                                        : flexRender(
                                            header.column.columnDef.header,
                                            header.getContext()
                                        )}
                                </TableHead>
                            ))}
                        </TableRow>
                    ))}
                </TableHeader>

                <TableBody>
                    {table.getRowModel().rows.map((row) => (
                        <React.Fragment key={row.id}>
                            {rowRenderer(row, table)}
                        </React.Fragment>
                    ))}
                </TableBody>
            </Table>
        </ScrollArea>
    );
}