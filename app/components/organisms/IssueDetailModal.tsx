"use client";

import { use<PERSON>tom } from 'jotai';
import { selectedIssueAtom } from '@/store/tableStore';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { Calendar, User, Target, Clock, Hash } from "lucide-react";

interface Issue {
  identifier: string;
  title: string;
  labels: string[];
  project: string;
  assignee: string | null;
  dueDate: string;
  status: string;
  priority: string;
  description: string;
  createdAt: string;
  updatedAt: string;
  cycle: string;
  estimate: number;
}

interface IssueDetailModalProps {
  issues: Issue[];
}

export default function IssueDetailModal({ issues }: IssueDetailModalProps) {
  const [selectedIssueId, setSelectedIssueId] = useAtom(selectedIssueAtom);
  
  const selectedIssue = issues.find(issue => issue.identifier === selectedIssueId);
  const isOpen = !!selectedIssue;

  const handleClose = () => {
    setSelectedIssueId(null);
  };

  if (!selectedIssue) return null;

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const getPriorityColor = (priority: string) => {
    switch (priority.toLowerCase()) {
      case 'urgent': return 'bg-red-100 text-red-800 border-red-200';
      case 'high': return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'low': return 'bg-green-100 text-green-800 border-green-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'todo': return 'bg-gray-100 text-gray-800 border-gray-200';
      case 'in progress': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'done': return 'bg-green-100 text-green-800 border-green-200';
      case 'cancelled': return 'bg-red-100 text-red-800 border-red-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Hash className="h-5 w-5 text-muted-foreground" />
            {selectedIssue.identifier}
          </DialogTitle>
        </DialogHeader>
        
        <div className="space-y-6">
          {/* Title */}
          <div>
            <h2 className="text-xl font-semibold">{selectedIssue.title}</h2>
          </div>

          {/* Status and Priority */}
          <div className="flex gap-3">
            <Badge className={getStatusColor(selectedIssue.status)}>
              {selectedIssue.status}
            </Badge>
            <Badge className={getPriorityColor(selectedIssue.priority)}>
              {selectedIssue.priority} Priority
            </Badge>
          </div>

          {/* Metadata Grid */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <User className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm font-medium">Assignee:</span>
                <span className="text-sm">{selectedIssue.assignee || 'Unassigned'}</span>
              </div>
              
              <div className="flex items-center gap-2">
                <Target className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm font-medium">Project:</span>
                <span className="text-sm">{selectedIssue.project}</span>
              </div>
              
              <div className="flex items-center gap-2">
                <Clock className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm font-medium">Cycle:</span>
                <span className="text-sm">{selectedIssue.cycle}</span>
              </div>
            </div>
            
            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm font-medium">Due Date:</span>
                <span className="text-sm">{formatDate(selectedIssue.dueDate)}</span>
              </div>
              
              <div className="flex items-center gap-2">
                <span className="text-sm font-medium">Estimate:</span>
                <span className="text-sm">{selectedIssue.estimate} points</span>
              </div>
              
              <div className="flex items-center gap-2">
                <span className="text-sm font-medium">Created:</span>
                <span className="text-sm">{formatDate(selectedIssue.createdAt)}</span>
              </div>
            </div>
          </div>

          {/* Labels */}
          {selectedIssue.labels && selectedIssue.labels.length > 0 && (
            <div>
              <span className="text-sm font-medium mb-2 block">Labels:</span>
              <div className="flex flex-wrap gap-2">
                {selectedIssue.labels.map((label, index) => (
                  <Badge key={index} variant="outline" className="text-xs">
                    {label}
                  </Badge>
                ))}
              </div>
            </div>
          )}

          {/* Description */}
          <div>
            <span className="text-sm font-medium mb-2 block">Description:</span>
            <div className="text-sm text-muted-foreground bg-muted/50 p-3 rounded-md">
              {selectedIssue.description || 'No description provided.'}
            </div>
          </div>

          {/* Timestamps */}
          <div className="text-xs text-muted-foreground border-t pt-3">
            <div>Created: {formatDate(selectedIssue.createdAt)}</div>
            <div>Updated: {formatDate(selectedIssue.updatedAt)}</div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
