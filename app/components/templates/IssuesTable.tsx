"use client";

 import {issueColumns} from "@/lib/issueColumnsSorted"; // Column definitions
import {useIssuesTable} from "@/hooks/useIssuesTable"; // Custom hook for TanStack Table logic
import GenericTable from "../organisms/GenericTable"; // Generic table structure
import TableRowRenderer from "../organisms/TableRowRenderer"; // Component to render individual rows
import React from "react";

// Define Issue type here if it's not in a shared types file
type Issue = {
    identifier: string;
    title: string;
    labels: string[];
    project: string;
    assignee: string | null;
    dueDate: string;
    status: string;
    priority: string;
    description: string;
    createdAt: string;
    updatedAt: string;
    cycle: string;
    estimate: number;
};

interface IssuesTableProps {
    data: Issue[];
     groupBy: "status" | "assignee" | "cycle" | "estimate";
}

export default function IssuesTable({data, groupBy}: IssuesTableProps) {


    const {table} = useIssuesTable({
        data,
        columns: issueColumns,
        groupBy,
    });



    // Define the row renderer function to be passed to GenericTable
    // This function ensures TableRowRenderer gets the correct props
    const renderIssueRow = (row: any, tableInstance: any) => (
        <TableRowRenderer
            row={row}
            groupBy={groupBy}
            tableInstance={tableInstance}
        />
    );

    return (
        <GenericTable<Issue> table={table} rowRenderer={renderIssueRow}/>
    );
}