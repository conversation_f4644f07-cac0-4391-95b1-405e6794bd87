"use client";

import TableRowRenderer from "../organisms/TableRowRenderer";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Table,
  TableBody,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { useIssuesData } from "@/hooks/useIssuesData";
import { issueColumns } from "@/lib/issue-columns";
import {
  getCoreRowModel,
  useReactTable,
  flexRender,
  SortingState,
  GroupingState,
  getSortedRowModel,
  getExpandedRowModel,
  getGroupedRowModel,
} from "@tanstack/react-table";
import { Minus, Plus } from "lucide-react";
import React, { useEffect } from "react";
import { useState } from "react";

type Issue = {
  identifier: string;
  title: string;
  labels: string[];
  project: string;
  assignee: string | null;
  dueDate: string;
  status: string;
  priority: string;
  description: string;
  createdAt: string;
  updatedAt: string;
  cycle: string;
  estimate: number;
};
interface IssuesTableProps {
  data: Issue[];
  groupBy: "status" | "project";
}
export default function IssuesTable({ data, groupBy }: IssuesTableProps) {
  const { isLoading } = useIssuesData();
  const [sorting, setSorting] = useState<SortingState>([]);
  const [grouping] = useState<GroupingState>([groupBy]);
  const [expanded, setExpanded] = useState<Record<string, boolean>>({});

  const table = useReactTable({
    data,
    columns: issueColumns,
    state: { sorting, grouping, expanded },
    onSortingChange: setSorting,
    onExpandedChange: setExpanded,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getGroupedRowModel: getGroupedRowModel(),
    getExpandedRowModel: getExpandedRowModel(),
    getRowCanExpand: () => true,
  });

  useEffect(() => {
    // force delay to compute table groping process might cause issue if groping take longer time in long data set
    const timeout = setTimeout(() => {
      // uncomment ungroping all the rows
      // const groupedRowIds = table
      //   .getRowModel()
      //   .rows.filter((row) => row.getIsGrouped())
      //   .reduce((acc, row) => {
      //     acc[row.id] = true;
      //     return acc;
      //   }, {} as Record<string, boolean>);
      // setExpanded(groupedRowIds);

      const firstGroupedRow = table
        .getRowModel()
        .rows.find((row) => row.getIsGrouped());

      if (firstGroupedRow) {
        setExpanded({ [firstGroupedRow.id]: true });
      } else {
        setExpanded({}); // No grouped rows, so nothing to expand
      }
    }, 0);

    return () => clearTimeout(timeout);
  }, [data, table, groupBy]);

  if (isLoading) return <div className="p-4">Loading...</div>;

  return (
    <ScrollArea className="rounded border p-4 h-[32rem]">
      <Table className="relative">
        <TableHeader className="sticky top-0 bg-background z-10">
          {table.getHeaderGroups().map((headerGroup) => (
            <TableRow key={headerGroup.id}>
              {headerGroup.headers.map((header) => (
                <TableHead key={header.id}>
                  {header.isPlaceholder
                    ? null
                    : flexRender(
                        header.column.columnDef.header,
                        header.getContext()
                      )}
                </TableHead>
              ))}
            </TableRow>
          ))}
        </TableHeader>

        <TableBody>
          {table.getRowModel().rows.map((row) => (
            <React.Fragment key={row.id}>
              <TableRowRenderer
                row={row}
                groupBy={groupBy}
                tableInstance={table}
              />
            </React.Fragment>
          ))}
        </TableBody>
      </Table>
    </ScrollArea>
  );
}
