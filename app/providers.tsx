"use client";

import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { Provider as <PERSON><PERSON><PERSON><PERSON>ider } from "jotai";
import ThemeProvider from "./providers/ThemeProvider";
import { ReactNode, useState } from "react";

export default function Providers({ children }: { children: ReactNode }) {
  const [queryClient] = useState(() => new QueryClient());

  return (
    <JotaiProvider>
      <ThemeProvider>
        <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
      </ThemeProvider>
    </JotaiProvider>
  );
}
