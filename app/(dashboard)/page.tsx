"use client";

import FilterDropdown from "@/app/components/molecules/FilterDropdown";
import SearchInput from "@/app/components/molecules/SearchInput";
import GroupByDropdown, { GroupByOption } from "@/app/components/molecules/GroupByDropdown";
import IssuesTable from "@/app/components/templates/IssuesTable";
import {useIssuesData} from "@/hooks/useIssuesData";
import {useState} from "react";

export default function IssuesPage() {
    const {data: issues = [], isLoading} = useIssuesData();

    const [search, setSearch] = useState("");
    const [statusFilter, setStatusFilter] = useState("");
    const [groupBy, setGroupBy] = useState<GroupByOption>("none");

    const filtered = issues.filter((issue) => {
        return (
            (!search ||
                issue.title.toLowerCase().includes(search.toLowerCase()) ||
                issue.identifier.toLowerCase().includes(search.toLowerCase())) &&
            (!statusFilter || issue.status === statusFilter)
        );
    });

    const uniqueStatuses = [...new Set(issues.map((i) => i.status))];

    if (isLoading) {
        return <div className="p-4 text-center">Loading issues...</div>; // Display loading message here
    }

    return (
        <div className="space-y-4">
            <div className="flex gap-4 flex-wrap p-4">
                <SearchInput value={search} onChange={setSearch}/>
                <FilterDropdown
                    value={statusFilter}
                    onChange={setStatusFilter}
                    options={uniqueStatuses}
                    placeholder="Status"
                />
                <GroupByDropdown value={groupBy} onChange={setGroupBy} />
            </div>
            <IssuesTable data={filtered} groupBy={groupBy} />
        </div>
    );
}
