"use client";

import { useAtomValue } from 'jotai';
import { groupByAtom } from '@/store/tableStore';
import SearchInput from "@/app/components/molecules/SearchInput";
import GroupByDropdown from "@/app/components/molecules/GroupByDropdown";
import FiltersBar from "@/app/components/molecules/FiltersBar";
import IssuesTable from "@/app/components/templates/IssuesTable";
import IssueDetailModal from "@/app/components/organisms/IssueDetailModal";
import { useIssuesData } from "@/hooks/useIssuesData";
import { useFilteredData } from "@/hooks/useFilteredData";

export default function IssuesPage() {
    const { data: issues = [], isLoading } = useIssuesData();
    const groupBy = useAtomValue(groupByAtom);
    const filteredData = useFilteredData(issues);

    if (isLoading) {
        return <div className="p-4 text-center">Loading issues...</div>;
    }

    return (
        <div className="space-y-6 py-6">
            <div className="space-y-4 px-4">
                <div className="flex gap-4 flex-wrap items-center">
                    <SearchInput />
                    <GroupByDropdown />
                </div>
                <FiltersBar data={issues} />
            </div>
            <div className="px-4">
                <IssuesTable data={filteredData} groupBy={groupBy} />
            </div>
            <IssueDetailModal issues={issues} />
        </div>
    );
}
