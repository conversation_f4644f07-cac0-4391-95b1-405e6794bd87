"use client";

import { use<PERSON>tom, useSet<PERSON>tom } from 'jotai';
import { themeAtom, initializeThemeAtom } from '@/store/themeStore';
import { ReactNode, useEffect } from 'react';

interface ThemeProviderProps {
  children: ReactNode;
}

export default function ThemeProvider({ children }: ThemeProviderProps) {
  const [theme] = useAtom(themeAtom);
  const initializeTheme = useSetAtom(initializeThemeAtom);

  useEffect(() => {
    // Initialize theme on mount
    initializeTheme();
  }, [initializeTheme]);

  useEffect(() => {
    // Apply theme changes
    if (typeof window !== 'undefined') {
      document.documentElement.classList.remove('light', 'dark');
      document.documentElement.classList.add(theme);
    }
  }, [theme]);

  return <>{children}</>;
}
