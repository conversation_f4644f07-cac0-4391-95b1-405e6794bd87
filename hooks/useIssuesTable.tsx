import {
    useReactTable,
    getCoreRowModel,
    getSortedRowModel,
    getGroupedRowModel,
    getExpandedRowModel,
    SortingState,
    GroupingState,
    ExpandedState,
    ColumnDef,
    Table as ReactTableInstance,
} from "@tanstack/react-table";
import React, { useState, useEffect } from "react";

type Issue = {
    identifier: string;
    title: string;
    labels: string[];
    project: string;
    assignee: string | null;
    dueDate: string;
    status: string;
    priority: string;
    description: string;
    createdAt: string;
    updatedAt: string;
    cycle: string;
    estimate: number;
};

interface UseIssuesTableOptions {
    data: Issue[];
    columns: ColumnDef<Issue>[];
    groupBy: "status" | "project";
}

interface UseIssuesTableReturn {
    table: ReactTableInstance<Issue>;
    sorting: SortingState;
    setSorting: React.Dispatch<React.SetStateAction<SortingState>>;
    expanded: ExpandedState;
    setExpanded: React.Dispatch<React.SetStateAction<ExpandedState>>;
    grouping: GroupingState;
}

export const useIssuesTable = ({
                                   data,
                                   columns,
                                   groupBy,
                               }: UseIssuesTableOptions): UseIssuesTableReturn => {
    const [sorting, setSorting] = useState<SortingState>([]);
    const [grouping] = useState<GroupingState>([groupBy]);
    const [expanded, setExpanded] = useState<ExpandedState>({});

    const table = useReactTable({
        data,
        columns,
        state: { sorting, grouping, expanded },
        onSortingChange: setSorting,
        onExpandedChange: setExpanded,
        getCoreRowModel: getCoreRowModel(),
        getSortedRowModel: getSortedRowModel(),
        getGroupedRowModel: getGroupedRowModel(),
        getExpandedRowModel: getExpandedRowModel(),
        getRowCanExpand: () => true,
    });

    useEffect(() => {
        const timeout = setTimeout(() => {
            const firstGroupedRow = table
                .getRowModel()
                .rows.find((row) => row.getIsGrouped());

            if (firstGroupedRow) {
                setExpanded({ [firstGroupedRow.id]: true });
            } else {
                setExpanded({});
            }
        }, 0);

        return () => clearTimeout(timeout);
    }, [data, table, groupBy]);

    return { table, sorting, setSorting, expanded, setExpanded, grouping };
};