import { useMemo, useCallback } from 'react';
import { useAtomValue } from 'jotai';
import { searchAtom, filters<PERSON>tom, sortAtom, paginationAtom } from '@/store/tableStore';

interface Issue {
  identifier: string;
  title: string;
  labels: string[];
  project: string;
  assignee: string | null;
  dueDate: string;
  status: string;
  priority: string;
  description: string;
  createdAt: string;
  updatedAt: string;
  cycle: string;
  estimate: number;
}

// Priority order for sorting
const priorityOrder: Record<string, number> = {
  "urgent": 4,
  "high": 3,
  "medium": 2,
  "low": 1,
  "none": 0,
};

// Memoized filter functions
const createSearchFilter = (search: string) => {
  if (!search) return null;
  const searchLower = search.toLowerCase();
  return (issue: Issue) =>
    issue.title.toLowerCase().includes(searchLower) ||
    issue.identifier.toLowerCase().includes(searchLower);
};

const createStatusFilter = (status: string) =>
  status ? (issue: Issue) => issue.status === status : null;

const createProjectFilter = (project: string) =>
  project ? (issue: Issue) => issue.project === project : null;

const createPriorityFilter = (priority: string) =>
  priority ? (issue: Issue) => issue.priority === priority : null;

const createAssigneeFilter = (assignee: string) =>
  assignee ? (issue: Issue) => issue.assignee === assignee : null;

const createCycleFilter = (cycle: string) =>
  cycle ? (issue: Issue) => issue.cycle === cycle : null;

export function useFilteredData(data: Issue[]) {
  const search = useAtomValue(searchAtom);
  const filters = useAtomValue(filtersAtom);
  const sort = useAtomValue(sortAtom);
  const pagination = useAtomValue(paginationAtom);

  // Memoized filter functions
  const searchFilter = useMemo(() => createSearchFilter(search), [search]);
  const statusFilter = useMemo(() => createStatusFilter(filters.status), [filters.status]);
  const projectFilter = useMemo(() => createProjectFilter(filters.project), [filters.project]);
  const priorityFilter = useMemo(() => createPriorityFilter(filters.priority), [filters.priority]);
  const assigneeFilter = useMemo(() => createAssigneeFilter(filters.assignee), [filters.assignee]);
  const cycleFilter = useMemo(() => createCycleFilter(filters.cycle), [filters.cycle]);

  // Memoized sort function
  const sortFunction = useMemo(() => {
    if (!sort.field) return null;

    return (a: Issue, b: Issue) => {
      let aValue: any;
      let bValue: any;

      switch (sort.field) {
        case 'createdAt':
        case 'dueDate':
        case 'updatedAt':
          aValue = new Date(a[sort.field]).getTime();
          bValue = new Date(b[sort.field]).getTime();
          break;
        case 'priority':
          aValue = priorityOrder[a.priority?.toLowerCase()] || 0;
          bValue = priorityOrder[b.priority?.toLowerCase()] || 0;
          break;
        case 'estimate':
          aValue = a.estimate || 0;
          bValue = b.estimate || 0;
          break;
        default:
          aValue = a[sort.field!];
          bValue = b[sort.field!];
      }

      if (aValue < bValue) return sort.direction === 'asc' ? -1 : 1;
      if (aValue > bValue) return sort.direction === 'asc' ? 1 : -1;
      return 0;
    };
  }, [sort]);

  // Memoized filtered and sorted data
  const filteredAndSortedData = useMemo(() => {
    let filtered = [...data];

    // Apply all filters
    const activeFilters = [
      searchFilter,
      statusFilter,
      projectFilter,
      priorityFilter,
      assigneeFilter,
      cycleFilter,
    ].filter(Boolean);

    for (const filter of activeFilters) {
      filtered = filtered.filter(filter!);
    }

    // Apply sorting
    if (sortFunction) {
      filtered.sort(sortFunction);
    }

    return filtered;
  }, [data, searchFilter, statusFilter, projectFilter, priorityFilter, assigneeFilter, cycleFilter, sortFunction]);

  // Memoized pagination data
  const paginatedData = useMemo(() => {
    const startIndex = pagination.pageIndex * pagination.pageSize;
    const endIndex = startIndex + pagination.pageSize;
    return filteredAndSortedData.slice(startIndex, endIndex);
  }, [filteredAndSortedData, pagination.pageIndex, pagination.pageSize]);

  // Return both paginated data and metadata
  return useMemo(() => ({
    data: paginatedData,
    totalCount: filteredAndSortedData.length,
    pageCount: Math.ceil(filteredAndSortedData.length / pagination.pageSize),
    currentPage: pagination.pageIndex,
    pageSize: pagination.pageSize,
    hasNextPage: pagination.pageIndex < Math.ceil(filteredAndSortedData.length / pagination.pageSize) - 1,
    hasPreviousPage: pagination.pageIndex > 0,
  }), [paginatedData, filteredAndSortedData.length, pagination]);
}
