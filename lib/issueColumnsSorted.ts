// src/lib/issueColumns.ts
import {ColumnDef} from "@tanstack/react-table";
import { ArrowUpDown, ArrowU<PERSON>, ArrowDown } from "lucide-react";
import { Button } from "@/components/ui/button";

// Priority order for sorting
const priorityOrder: Record<string, number> = {
    "urgent": 4,
    "high": 3,
    "medium": 2,
    "low": 1,
    "none": 0,
};

// Helper function to create sortable header
const createSortableHeader = (title: string) => ({ column }: any) => {
    const isSorted = column.getIsSorted();
    return (
        <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            className="h-auto p-0 font-medium hover:bg-transparent"
        >
            {title}
            {isSorted === "asc" ? (
                <ArrowUp className="ml-2 h-4 w-4" />
            ) : isSorted === "desc" ? (
                <ArrowDown className="ml-2 h-4 w-4" />
            ) : (
                <ArrowUpDown className="ml-2 h-4 w-4" />
            )}
        </Button>
    );
};

// Define Issue type if not already globally available or import it
type Issue = {
    identifier: string;
    title: string;
    labels: string[];
    project: string;
    assignee: string | null;
    dueDate: string;
    status: string;
    priority: string;
    description: string;
    createdAt: string;
    updatedAt: string;
    cycle: string;
    estimate: number;
};

export const issueColumns: ColumnDef<Issue>[] = [
    {
        accessorKey: "identifier",
        header: "Identifier",
    },
    {
        accessorKey: "title",
        header: "Title",
    },
    {
        accessorKey: "labels",
        header: "Labels",
    },
    {
        accessorKey: "project",
        header: "Project",
        cell: ({row}) => row.original.project || "-",
    },
    {
        accessorKey: "assignee",
        header: "Assignee",
        cell: ({row}) => row.original.assignee || "-",
    },
    {
        accessorKey: "cycle",
        header: "Cycle",
    },
    {
        accessorKey: "priority",
        header: createSortableHeader("Priority"),
        sortingFn: (rowA, rowB) => {
            const priorityA = priorityOrder[rowA.original.priority?.toLowerCase()] || 0;
            const priorityB = priorityOrder[rowB.original.priority?.toLowerCase()] || 0;
            return priorityA - priorityB;
        },
        enableSorting: true,
    },
    {
        accessorKey: "status",
        header: "Status",
    },
    {
        accessorKey: "estimate",
        header: createSortableHeader("Estimate"),
        sortingFn: (rowA, rowB) => {
            const estimateA = rowA.original.estimate || 0;
            const estimateB = rowB.original.estimate || 0;
            return estimateA - estimateB;
        },
        enableSorting: true,
    },
    {
        accessorKey: "createdAt",
        header: createSortableHeader("Created At"),
        cell: (info) => new Date(info.getValue() as string).toLocaleDateString(),
        sortingFn: (rowA, rowB) => {
            const dateA = new Date(rowA.original.createdAt).getTime();
            const dateB = new Date(rowB.original.createdAt).getTime();
            return dateA - dateB;
        },
        enableSorting: true,
    },
    {
        accessorKey: "dueDate",
        header: createSortableHeader("Due Date"),
        cell: (info) => new Date(info.getValue() as string).toLocaleDateString(),
        sortingFn: (rowA, rowB) => {
            const dateA = new Date(rowA.original.dueDate).getTime();
            const dateB = new Date(rowB.original.dueDate).getTime();
            return dateA - dateB;
        },
        enableSorting: true,
    },
    {
        accessorKey: "updatedAt",
        header: createSortableHeader("Updated At"),
        cell: (info) => new Date(info.getValue() as string).toLocaleDateString(),
        sortingFn: (rowA, rowB) => {
            const dateA = new Date(rowA.original.updatedAt).getTime();
            const dateB = new Date(rowB.original.updatedAt).getTime();
            return dateA - dateB;
        },
        enableSorting: true,
    },
];
