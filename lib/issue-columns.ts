// src/lib/issue-columns.ts
import { ColumnDef } from "@tanstack/react-table";

// Define Issue type if not already globally available or import it
type Issue = {
  identifier: string;
  title: string;
  labels: string[];
  project: string;
  assignee: string | null;
  dueDate: string;
  status: string;
  priority: string;
  description: string;
  createdAt: string;
  updatedAt: string;
  cycle: string;
  estimate: number;
};

export const issueColumns: ColumnDef<Issue>[] = [
  {
    accessorKey: "identifier",
    header: "Identifier",
    cell: (info) => info.getValue(),
  },
  {
    accessorKey: "title",
    header: "Title",
    cell: (info) => info.getValue(),
  },
  {
    accessorKey: "project",
    header: "Project",
    cell: ({ row }) => row.original.project || "-",
  },
  {
    accessorKey: "assignee",
    header: "Assignee",
    cell: ({ row }) => row.original.assignee || "-",
  },
  {
    accessorKey: "priority",
    header: "Priority",
    cell: (info) => info.getValue(),
  },
  {
    accessorKey: "status",
    header: "Status",
    cell: (info) => info.getValue(),
  },
  {
    accessorKey: "createdAt",
    header: "Created At",
    cell: (info) => new Date(info.getValue() as string).toLocaleDateString(),
  },
  {
    accessorKey: "updatedAt",
    header: "Updated At",
    cell: (info) => new Date(info.getValue() as string).toLocaleDateString(),
  },
];
