import { atom } from 'jotai';
import { tableSettingsAtom } from './tableStore';

export type Theme = 'light' | 'dark';

// Theme atom derived from table settings
export const themeAtom = atom(
  (get) => get(tableSettingsAtom).theme,
  (get, set, newTheme: Theme) => {
    const current = get(tableSettingsAtom);
    set(tableSettingsAtom, { ...current, theme: newTheme });

    // Apply theme to document
    if (typeof window !== 'undefined') {
      document.documentElement.classList.remove('light', 'dark');
      document.documentElement.classList.add(newTheme);
    }
  }
);

// Derived atom for theme toggle
export const toggleThemeAtom = atom(
  null,
  (get, set) => {
    const currentTheme = get(themeAtom);
    const newTheme = currentTheme === 'light' ? 'dark' : 'light';
    set(themeAtom, newTheme);
  }
);

// Initialize theme atom
export const initializeThemeAtom = atom(
  null,
  (get, set) => {
    if (typeof window !== 'undefined') {
      const theme = get(themeAtom);
      document.documentElement.classList.remove('light', 'dark');
      document.documentElement.classList.add(theme);
    }
  }
);
