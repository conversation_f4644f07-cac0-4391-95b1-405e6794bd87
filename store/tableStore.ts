import { atom } from 'jotai';
import { atomWithStorage } from 'jotai/utils';

// Types
export type GroupByOption = "none" | "status" | "assignee" | "cycle" | "estimate";
export type SortField = "createdAt" | "dueDate" | "updatedAt" | "priority" | "estimate";
export type SortDirection = "asc" | "desc";

export interface SortState {
  field: SortField | null;
  direction: SortDirection;
}

export interface FilterState {
  status: string;
  project: string;
  priority: string;
  assignee: string;
  cycle: string;
}

export interface TableSettings {
  search: string;
  sort: SortState;
  filters: FilterState;
  groupBy: GroupByOption;
  selectedIssueId: string | null;
}

// Default state
const defaultTableSettings: TableSettings = {
  search: "",
  sort: { field: null, direction: "asc" },
  filters: {
    status: "",
    project: "",
    priority: "",
    assignee: "",
    cycle: "",
  },
  groupBy: "none",
  selectedIssueId: null,
};

// Persistent atoms using localStorage
export const tableSettingsAtom = atomWithStorage<TableSettings>(
  'issueTableSettings',
  defaultTableSettings
);

// Derived atoms for individual settings
export const searchAtom = atom(
  (get) => get(tableSettingsAtom).search,
  (get, set, newSearch: string) => {
    const current = get(tableSettingsAtom);
    set(tableSettingsAtom, { ...current, search: newSearch });
  }
);

export const sortAtom = atom(
  (get) => get(tableSettingsAtom).sort,
  (get, set, newSort: SortState) => {
    const current = get(tableSettingsAtom);
    set(tableSettingsAtom, { ...current, sort: newSort });
  }
);

export const filtersAtom = atom(
  (get) => get(tableSettingsAtom).filters,
  (get, set, newFilters: FilterState) => {
    const current = get(tableSettingsAtom);
    set(tableSettingsAtom, { ...current, filters: newFilters });
  }
);

export const groupByAtom = atom(
  (get) => get(tableSettingsAtom).groupBy,
  (get, set, newGroupBy: GroupByOption) => {
    const current = get(tableSettingsAtom);
    set(tableSettingsAtom, { ...current, groupBy: newGroupBy });
  }
);

export const selectedIssueAtom = atom(
  (get) => get(tableSettingsAtom).selectedIssueId,
  (get, set, newSelectedId: string | null) => {
    const current = get(tableSettingsAtom);
    set(tableSettingsAtom, { ...current, selectedIssueId: newSelectedId });
  }
);

// Reset atom
export const resetTableSettingsAtom = atom(
  null,
  (get, set) => {
    set(tableSettingsAtom, defaultTableSettings);
  }
);
