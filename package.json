{"name": "issue-dashboard", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@tanstack/react-query": "^5.76.2", "@tanstack/react-table": "^8.21.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "jotai": "^2.12.4", "lucide-react": "^0.511.0", "next": "15.1.8", "react": "^19.0.0", "react-dom": "^19.0.0", "tailwind-merge": "^3.3.0", "tailwindcss-animate": "^1.0.7", "ui": "shadcn/ui"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.1.8", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}